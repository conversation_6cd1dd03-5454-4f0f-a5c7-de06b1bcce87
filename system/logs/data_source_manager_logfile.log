[data_source_manager] [2025-09-04 13:32:16] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-04 13:32:16] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 13:32:16] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 13:32:16] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 14:25:45] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-04 14:25:45] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 14:25:45] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 14:25:45] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 14:49:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-04 14:49:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 14:49:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 14:49:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 14:53:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-04 14:53:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 14:53:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 14:53:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 15:06:15] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-04 15:06:15] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 15:06:15] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-04 15:06:15] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:28:54] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 13:28:54] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:28:54] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:28:54] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:28:56] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 13:28:56] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:28:56] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:28:56] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.id => sketchup_id AND sketchup.id => sketchup_id
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.sold_to_name => sold_to_name AND sketchup.sold_to_name => sold_to_name
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.sold_to_number => sold_to_number AND sketchup.sold_to_number => sold_to_number
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.vendor_name => vendor_name AND sketchup.vendor_name => vendor_name
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.reseller_number => reseller_name AND sketchup.reseller_number => reseller_name
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.reseller_vendor_id => vendor_id AND sketchup.reseller_vendor_id => vendor_id
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: end_customer_vendor_id -> vendor_id (score: 67.4) loses to reseller_vendor_id (score: 67.4)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for end_customer_vendor_id: found 3 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback end_customer_vendor_id -> vendor_id: score 67.4 loses to existing reseller_vendor_id (score: 67.4)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1539] Fallback success: end_customer_vendor_id -> company_name (score: 65.8, confidence: 90)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_vendor_id => company_name AND sketchup.end_customer_vendor_id => company_name
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:325] Alias conflict: end_customer_name -> company_name (score: 77) replaces end_customer_vendor_id (score: 67.4)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_name => company_name AND sketchup.end_customer_name => company_name
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_address_1 => address AND sketchup.end_customer_address_1 => address
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_address_2 => end_customer_address_2 AND sketchup.end_customer_address_2 => end_customer_address_2
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_address_3 => end_customer_address_3 AND sketchup.end_customer_address_3 => end_customer_address_3
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_city => city AND sketchup.end_customer_city => city
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_state => state AND sketchup.end_customer_state => state
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_zip_code => postal_code AND sketchup.end_customer_zip_code => postal_code
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_country => country AND sketchup.end_customer_country => country
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_account_type => end_customer_account_type AND sketchup.end_customer_account_type => end_customer_account_type
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_contact_name => contact_name AND sketchup.end_customer_contact_name => contact_name
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_contact_email => email AND sketchup.end_customer_contact_email => email
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_contact_phone => end_customer_contact_phone AND sketchup.end_customer_contact_phone => end_customer_contact_phone
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_industry_segment => end_customer_industry_segment AND sketchup.end_customer_industry_segment => end_customer_industry_segment
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_program_name => product_name AND sketchup.agreement_program_name => product_name
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_number => subscription_reference AND sketchup.agreement_number => subscription_reference
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_start_date => sketchup_agreement_start_date AND sketchup.agreement_start_date => sketchup_agreement_start_date
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_end_date => sketchup_agreement_end_date AND sketchup.agreement_end_date => sketchup_agreement_end_date
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_terms => agreement_terms AND sketchup.agreement_terms => agreement_terms
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_type => agreement_type AND sketchup.agreement_type => agreement_type
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_status => agreement_status AND sketchup.agreement_status => agreement_status
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_support_level => agreement_support_level AND sketchup.agreement_support_level => agreement_support_level
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_days_due => sketchup_agreement_days_due AND sketchup.agreement_days_due => sketchup_agreement_days_due
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_autorenew => sketchup_agreement_autorenew AND sketchup.agreement_autorenew => sketchup_agreement_autorenew
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: product_name -> product_name (score: 79) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for product_name: found 3 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback product_name -> product_name: score 79 loses to existing agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback product_name -> company_name: score 63.4 loses to existing end_customer_name (score: 77)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback product_name -> contact_name: score 59.4 loses to existing end_customer_contact_name (score: 76)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1544] No suitable fallback found for product_name
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_name => sketchup_product_name AND sketchup.product_name => sketchup_product_name
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: product_family -> product_name (score: 79) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for product_family: found 2 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback product_family -> product_name: score 79 loses to existing agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1539] Fallback success: product_family -> product_family (score: 68.29, confidence: 100)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_family => product_family AND sketchup.product_family => product_family
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_market_segment => product_market_segment AND sketchup.product_market_segment => product_market_segment
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: product_release -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for product_release: found 1 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback product_release -> product_name: score 70.2 loses to existing agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1544] No suitable fallback found for product_release
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_release => sketchup_product_release AND sketchup.product_release => sketchup_product_release
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: product_type -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for product_type: found 2 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback product_type -> product_name: score 70.2 loses to existing agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1539] Fallback success: product_type -> product_type (score: 67.75, confidence: 100)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_type => product_type AND sketchup.product_type => product_type
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: product_deployment -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for product_deployment: found 2 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback product_deployment -> product_name: score 70.2 loses to existing agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1539] Fallback success: product_deployment -> product_deployment (score: 67.33, confidence: 100)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_deployment => product_deployment AND sketchup.product_deployment => product_deployment
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: product_sku -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for product_sku: found 3 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback product_sku -> product_name: score 70.2 loses to existing agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1539] Fallback success: product_sku -> product_sku (score: 69, confidence: 100)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_sku => product_sku AND sketchup.product_sku => product_sku
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_sku_description => product_sku_description AND sketchup.product_sku_description => product_sku_description
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: product_part -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for product_part: found 2 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback product_part -> product_name: score 70.2 loses to existing agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1539] Fallback success: product_part -> product_part (score: 67.75, confidence: 100)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_part => product_part AND sketchup.product_part => product_part
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_list_price => product_list_price AND sketchup.product_list_price => product_list_price
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: product_list_price_currency -> product_name (score: 70.2) loses to agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for product_list_price_currency: found 2 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback product_list_price_currency -> product_name: score 70.2 loses to existing agreement_program_name (score: 79)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback product_list_price_currency -> product_list_price: score 67.29 loses to existing product_list_price (score: 71.29)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1544] No suitable fallback found for product_list_price_currency
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_list_price_currency => sketchup_product_list_price_currency AND sketchup.product_list_price_currency => sketchup_product_list_price_currency
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: subscription_id -> subscription_reference (score: 94) loses to agreement_number (score: 94)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for subscription_id: found 1 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback subscription_id -> subscription_reference: score 94 loses to existing agreement_number (score: 94)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1544] No suitable fallback found for subscription_id
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_id => sketchup_subscription_id AND sketchup.subscription_id => sketchup_subscription_id
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: subscription_serial_number -> subscription_reference (score: 88) loses to agreement_number (score: 94)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for subscription_serial_number: found 2 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback subscription_serial_number -> subscription_reference: score 88 loses to existing agreement_number (score: 94)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1539] Fallback success: subscription_serial_number -> subscription_serial_number (score: 76, confidence: 100)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_serial_number => subscription_serial_number AND sketchup.subscription_serial_number => subscription_serial_number
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_status => status AND sketchup.subscription_status => status
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_quantity => quantity AND sketchup.subscription_quantity => quantity
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_start_date => sketchup_subscription_start_date AND sketchup.subscription_start_date => sketchup_subscription_start_date
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_end_date => sketchup_subscription_end_date AND sketchup.subscription_end_date => sketchup_subscription_end_date
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: subscription_contact_name -> contact_name (score: 73) loses to end_customer_contact_name (score: 76)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for subscription_contact_name: found 3 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback subscription_contact_name -> contact_name: score 73 loses to existing end_customer_contact_name (score: 76)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1539] Fallback success: subscription_contact_name -> end_customer_contact_name (score: 72, confidence: 100)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_contact_name => end_customer_contact_name AND sketchup.subscription_contact_name => end_customer_contact_name
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: subscription_contact_email -> email (score: 82) loses to end_customer_contact_email (score: 82)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for subscription_contact_email: found 2 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback subscription_contact_email -> email: score 82 loses to existing end_customer_contact_email (score: 82)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback subscription_contact_email -> contact_name: score 61.2 loses to existing end_customer_contact_name (score: 76)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1544] No suitable fallback found for subscription_contact_email
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_contact_email => sketchup_subscription_contact_email AND sketchup.subscription_contact_email => sketchup_subscription_contact_email
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_level => subscription_level AND sketchup.subscription_level => subscription_level
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_days_due => sketchup_subscription_days_due AND sketchup.subscription_days_due => sketchup_subscription_days_due
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.quotation_id => quotation_id AND sketchup.quotation_id => quotation_id
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.quotation_type => quotation_type AND sketchup.quotation_type => quotation_type
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:327] Alias conflict: quotation_vendor_id -> vendor_id (score: 67.4) loses to reseller_vendor_id (score: 67.4)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1511] Trying fallback for quotation_vendor_id: found 2 alternatives
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback quotation_vendor_id -> vendor_id: score 67.4 loses to existing reseller_vendor_id (score: 67.4)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1534] Fallback quotation_vendor_id -> vendor_name: score 58.89 loses to existing vendor_name (score: 68.29)
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:1544] No suitable fallback found for quotation_vendor_id
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_vendor_id => sketchup_quotation_vendor_id AND sketchup.quotation_vendor_id => sketchup_quotation_vendor_id
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_deal_registration_number => sketchup_quotation_deal_registration_number AND sketchup.quotation_deal_registration_number => sketchup_quotation_deal_registration_number
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.quotation_status => quotation_status AND sketchup.quotation_status => quotation_status
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_resellerpo_previous => sketchup_quotation_resellerpo_previous AND sketchup.quotation_resellerpo_previous => sketchup_quotation_resellerpo_previous
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_due_date => sketchup_quotation_due_date AND sketchup.quotation_due_date => sketchup_quotation_due_date
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.flaer_phase => flaer_phase AND sketchup.flaer_phase => flaer_phase
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.updated => sketchup_updated AND sketchup.updated => sketchup_updated
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.created_at => sketchup_created_at AND sketchup.created_at => sketchup_created_at
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.updated_at => sketchup_updated_at AND sketchup.updated_at => sketchup_updated_at
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:187] Generated 124 column aliases: {"autobooks_import_sketchup_data.id":"sketchup_id","sketchup.id":"sketchup_id","autobooks_import_sketchup_data.sold_to_name":"sold_to_name","sketchup.sold_to_name":"sold_to_name","autobooks_import_sketchup_data.sold_to_number":"sold_to_number","sketchup.sold_to_number":"sold_to_number","autobooks_import_sketchup_data.vendor_name":"vendor_name","sketchup.vendor_name":"vendor_name","autobooks_import_sketchup_data.reseller_number":"reseller_name","sketchup.reseller_number":"reseller_name","autobooks_import_sketchup_data.reseller_vendor_id":"vendor_id","sketchup.reseller_vendor_id":"vendor_id","autobooks_import_sketchup_data.end_customer_vendor_id":"company_name","sketchup.end_customer_vendor_id":"company_name","autobooks_import_sketchup_data.end_customer_name":"company_name","sketchup.end_customer_name":"company_name","autobooks_import_sketchup_data.end_customer_address_1":"address","sketchup.end_customer_address_1":"address","autobooks_import_sketchup_data.end_customer_address_2":"end_customer_address_2","sketchup.end_customer_address_2":"end_customer_address_2","autobooks_import_sketchup_data.end_customer_address_3":"end_customer_address_3","sketchup.end_customer_address_3":"end_customer_address_3","autobooks_import_sketchup_data.end_customer_city":"city","sketchup.end_customer_city":"city","autobooks_import_sketchup_data.end_customer_state":"state","sketchup.end_customer_state":"state","autobooks_import_sketchup_data.end_customer_zip_code":"postal_code","sketchup.end_customer_zip_code":"postal_code","autobooks_import_sketchup_data.end_customer_country":"country","sketchup.end_customer_country":"country","autobooks_import_sketchup_data.end_customer_account_type":"end_customer_account_type","sketchup.end_customer_account_type":"end_customer_account_type","autobooks_import_sketchup_data.end_customer_contact_name":"contact_name","sketchup.end_customer_contact_name":"contact_name","autobooks_import_sketchup_data.end_customer_contact_email":"email","sketchup.end_customer_contact_email":"email","autobooks_import_sketchup_data.end_customer_contact_phone":"end_customer_contact_phone","sketchup.end_customer_contact_phone":"end_customer_contact_phone","autobooks_import_sketchup_data.end_customer_industry_segment":"end_customer_industry_segment","sketchup.end_customer_industry_segment":"end_customer_industry_segment","autobooks_import_sketchup_data.agreement_program_name":"product_name","sketchup.agreement_program_name":"product_name","autobooks_import_sketchup_data.agreement_number":"subscription_reference","sketchup.agreement_number":"subscription_reference","autobooks_import_sketchup_data.agreement_start_date":"sketchup_agreement_start_date","sketchup.agreement_start_date":"sketchup_agreement_start_date","autobooks_import_sketchup_data.agreement_end_date":"sketchup_agreement_end_date","sketchup.agreement_end_date":"sketchup_agreement_end_date","autobooks_import_sketchup_data.agreement_terms":"agreement_terms","sketchup.agreement_terms":"agreement_terms","autobooks_import_sketchup_data.agreement_type":"agreement_type","sketchup.agreement_type":"agreement_type","autobooks_import_sketchup_data.agreement_status":"agreement_status","sketchup.agreement_status":"agreement_status","autobooks_import_sketchup_data.agreement_support_level":"agreement_support_level","sketchup.agreement_support_level":"agreement_support_level","autobooks_import_sketchup_data.agreement_days_due":"sketchup_agreement_days_due","sketchup.agreement_days_due":"sketchup_agreement_days_due","autobooks_import_sketchup_data.agreement_autorenew":"sketchup_agreement_autorenew","sketchup.agreement_autorenew":"sketchup_agreement_autorenew","autobooks_import_sketchup_data.product_name":"sketchup_product_name","sketchup.product_name":"sketchup_product_name","autobooks_import_sketchup_data.product_family":"product_family","sketchup.product_family":"product_family","autobooks_import_sketchup_data.product_market_segment":"product_market_segment","sketchup.product_market_segment":"product_market_segment","autobooks_import_sketchup_data.product_release":"sketchup_product_release","sketchup.product_release":"sketchup_product_release","autobooks_import_sketchup_data.product_type":"product_type","sketchup.product_type":"product_type","autobooks_import_sketchup_data.product_deployment":"product_deployment","sketchup.product_deployment":"product_deployment","autobooks_import_sketchup_data.product_sku":"product_sku","sketchup.product_sku":"product_sku","autobooks_import_sketchup_data.product_sku_description":"product_sku_description","sketchup.product_sku_description":"product_sku_description","autobooks_import_sketchup_data.product_part":"product_part","sketchup.product_part":"product_part","autobooks_import_sketchup_data.product_list_price":"product_list_price","sketchup.product_list_price":"product_list_price","autobooks_import_sketchup_data.product_list_price_currency":"sketchup_product_list_price_currency","sketchup.product_list_price_currency":"sketchup_product_list_price_currency","autobooks_import_sketchup_data.subscription_id":"sketchup_subscription_id","sketchup.subscription_id":"sketchup_subscription_id","autobooks_import_sketchup_data.subscription_serial_number":"subscription_serial_number","sketchup.subscription_serial_number":"subscription_serial_number","autobooks_import_sketchup_data.subscription_status":"status","sketchup.subscription_status":"status","autobooks_import_sketchup_data.subscription_quantity":"quantity","sketchup.subscription_quantity":"quantity","autobooks_import_sketchup_data.subscription_start_date":"sketchup_subscription_start_date","sketchup.subscription_start_date":"sketchup_subscription_start_date","autobooks_import_sketchup_data.subscription_end_date":"sketchup_subscription_end_date","sketchup.subscription_end_date":"sketchup_subscription_end_date","autobooks_import_sketchup_data.subscription_contact_name":"end_customer_contact_name","sketchup.subscription_contact_name":"end_customer_contact_name","autobooks_import_sketchup_data.subscription_contact_email":"sketchup_subscription_contact_email","sketchup.subscription_contact_email":"sketchup_subscription_contact_email","autobooks_import_sketchup_data.subscription_level":"subscription_level","sketchup.subscription_level":"subscription_level","autobooks_import_sketchup_data.subscription_days_due":"sketchup_subscription_days_due","sketchup.subscription_days_due":"sketchup_subscription_days_due","autobooks_import_sketchup_data.quotation_id":"quotation_id","sketchup.quotation_id":"quotation_id","autobooks_import_sketchup_data.quotation_type":"quotation_type","sketchup.quotation_type":"quotation_type","autobooks_import_sketchup_data.quotation_vendor_id":"sketchup_quotation_vendor_id","sketchup.quotation_vendor_id":"sketchup_quotation_vendor_id","autobooks_import_sketchup_data.quotation_deal_registration_number":"sketchup_quotation_deal_registration_number","sketchup.quotation_deal_registration_number":"sketchup_quotation_deal_registration_number","autobooks_import_sketchup_data.quotation_status":"quotation_status","sketchup.quotation_status":"quotation_status","autobooks_import_sketchup_data.quotation_resellerpo_previous":"sketchup_quotation_resellerpo_previous","sketchup.quotation_resellerpo_previous":"sketchup_quotation_resellerpo_previous","autobooks_import_sketchup_data.quotation_due_date":"sketchup_quotation_due_date","sketchup.quotation_due_date":"sketchup_quotation_due_date","autobooks_import_sketchup_data.flaer_phase":"flaer_phase","sketchup.flaer_phase":"flaer_phase","autobooks_import_sketchup_data.updated":"sketchup_updated","sketchup.updated":"sketchup_updated","autobooks_import_sketchup_data.created_at":"sketchup_created_at","sketchup.created_at":"sketchup_created_at","autobooks_import_sketchup_data.updated_at":"sketchup_updated_at","sketchup.updated_at":"sketchup_updated_at"}
[data_source_manager] [2025-09-05 13:33:49] [data_source_manager.class.php:267] Created data source: CSV Import: Sketchup with ID: 80
[data_source_manager] [2025-09-05 13:38:49] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 13:38:49] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:38:49] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:38:50] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:39:10] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:39:10] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:39:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:39:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:39:14] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:39:14] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:39:15] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:39:15] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:39:17] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 13:39:18] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:39:18] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:39:18] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:40:05] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:40:05] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:40:10] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:40:10] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:40:13] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:40:13] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:40:22] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 13:40:22] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:40:22] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:40:22] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:41:03] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:41:03] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:41:09] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:41:09] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:41:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 13:41:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:41:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:41:12] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:41:25] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:41:25] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:56:56] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 13:56:56] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:56:56] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:56:56] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:57:09] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:57:09] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:58:50] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 13:58:50] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:58:50] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:58:50] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:58:56] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 13:58:56] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:00:06] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 14:00:06] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:00:06] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:00:06] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:00:14] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:00:14] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:01:03] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 14:01:03] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:01:03] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:01:03] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:01:10] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:01:10] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:02:31] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 14:02:31] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:02:31] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:02:31] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:02:34] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 14:02:34] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:02:34] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:02:34] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:03:33] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 14:03:33] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:03:33] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:03:33] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:04:02] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 14:04:02] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:04:02] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:04:02] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:04:03] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 14:04:03] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:04:04] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:04:04] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:04:13] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-09-05 14:04:13] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:04:13] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-09-05 14:04:13] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `sold_to_name`, `sketchup`.`sold_to_number` AS `sold_to_number`, `sketchup`.`vendor_name` AS `vendor_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `vendor_id`, `sketchup`.`end_customer_vendor_id` AS `company_name`, `sketchup`.`end_customer_name` AS `company_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `sketchup_agreement_start_date`, `sketchup`.`agreement_end_date` AS `sketchup_agreement_end_date`, `sketchup`.`agreement_terms` AS `agreement_terms`, `sketchup`.`agreement_type` AS `agreement_type`, `sketchup`.`agreement_status` AS `agreement_status`, `sketchup`.`agreement_support_level` AS `agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_family`, `sketchup`.`product_market_segment` AS `product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `product_type`, `sketchup`.`product_deployment` AS `product_deployment`, `sketchup`.`product_sku` AS `product_sku`, `sketchup`.`product_sku_description` AS `product_sku_description`, `sketchup`.`product_part` AS `product_part`, `sketchup`.`product_list_price` AS `product_list_price`, `sketchup`.`product_list_price_currency` AS `sketchup_product_list_price_currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `subscription_serial_number`, `sketchup`.`subscription_status` AS `status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `end_customer_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `quotation_id`, `sketchup`.`quotation_type` AS `quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
