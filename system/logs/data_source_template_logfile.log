[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:55]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:64]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:66]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:70]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Found 10 total data sources
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 79: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:78]  Found matching data source ID 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:104] Using existing data source ID: 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:113] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":79,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 79 records"}
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:157] Updated data table storage configuration to use data source ID: 79
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:161] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:192] Retrieved 50 rows from data source ID: 79
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-04 13:32:16] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:349] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:55]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:64]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:66]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:70]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Found 10 total data sources
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 79: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:78]  Found matching data source ID 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:104] Using existing data source ID: 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:113] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":79,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 79 records"}
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:157] Updated data table storage configuration to use data source ID: 79
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:161] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:192] Retrieved 50 rows from data source ID: 79
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-04 14:25:45] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:349] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:55]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:64]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:66]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:70]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Found 10 total data sources
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 79: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:78]  Found matching data source ID 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:104] Using existing data source ID: 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:113] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":79,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 79 records"}
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:157] Updated data table storage configuration to use data source ID: 79
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:161] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:192] Retrieved 50 rows from data source ID: 79
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-04 14:49:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:349] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:55]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:64]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:66]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:70]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Found 10 total data sources
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 79: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:78]  Found matching data source ID 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:104] Using existing data source ID: 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:113] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":79,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 79 records"}
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:157] Updated data table storage configuration to use data source ID: 79
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:161] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:192] Retrieved 50 rows from data source ID: 79
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-04 14:53:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:349] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:55]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:64]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:66]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:70]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Found 10 total data sources
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 79: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:78]  Found matching data source ID 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:104] Using existing data source ID: 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:113] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":79,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 79 records"}
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:157] Updated data table storage configuration to use data source ID: 79
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:161] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:192] Retrieved 50 rows from data source ID: 79
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-04 15:06:15] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:349] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:55]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:64]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:66]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:70]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Found 10 total data sources
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 79: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:78]  Found matching data source ID 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:104] Using existing data source ID: 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:113] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":79,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 79 records"}
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:157] Updated data table storage configuration to use data source ID: 79
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:161] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:192] Retrieved 50 rows from data source ID: 79
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 13:28:54] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:349] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:55]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:64]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:66]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:70]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Found 10 total data sources
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 79: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:78]  Found matching data source ID 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:75]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:104] Using existing data source ID: 79 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:113] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":79,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 79 records"}
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:157] Updated data table storage configuration to use data source ID: 79
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:161] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:192] Retrieved 50 rows from data source ID: 79
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 13:28:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:349] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:151] Created data table storage configuration for data source ID: 80
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 13:38:49] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 13:39:17] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 13:39:18] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 13:39:18] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 13:40:22] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 13:41:12] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 13:56:56] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 13:58:50] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 14:00:06] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 14:01:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 14:02:31] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 14:02:34] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 14:03:33] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 14:04:02] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 14:04:03] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:56]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:57]  Processing table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:65]  Table exists: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:67]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:71]  Looking for data sources for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:72]  Found 10 total data sources
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 63: 'Autodesk_products' with table_name: 'products_autodesk_catalog'
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 80: 'CSV Import: Sketchup' with table_name: 'autobooks_import_sketchup_data'
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:79]  Found matching data source ID 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:76]  Checking data source ID 51: 'tstAutodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:105] Using existing data source ID: 80 for table: autobooks_import_sketchup_data
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:114] Table info for autobooks_import_sketchup_data: {"name":"autobooks_import_sketchup_data","display_name":"Autobooks Import Sketchup Data","category":"data_table","row_count":77,"columns":[{"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"},{"Field":"sold_to_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"sold_to_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"vendor_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"reseller_vendor_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_1","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_2","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_address_3","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_city","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_state","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_zip_code","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_country","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_account_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_contact_phone","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"end_customer_industry_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_program_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_number","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_terms","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_support_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"agreement_autorenew","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_family","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_market_segment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_release","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_deployment","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_sku_description","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_part","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"product_list_price_currency","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_serial_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_quantity","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_start_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_end_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_name","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_contact_email","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_level","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"subscription_days_due","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_id","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_type","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_vendor_id","Type":"int(11)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_deal_registration_number","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_status","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_resellerpo_previous","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"quotation_due_date","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"flaer_phase","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"updated","Type":"varchar(255)","Null":"YES","Key":"","Default":null,"Extra":""},{"Field":"created_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":""},{"Field":"updated_at","Type":"timestamp","Null":"YES","Key":"","Default":"current_timestamp()","Extra":"on update current_timestamp()"}],"primary_key":"id","has_data_json":false,"created_at":"created_at","description":"Data table with 62 columns and 77 records"}
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:158] Updated data table storage configuration to use data source ID: 80
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:162] Search criteria being passed: {"limit":50,"offset":0}
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:193] Retrieved 50 rows from data source ID: 80
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:194] Columns for data table: [{"label":"Sold to name","field":"sold_to_name","filter":true},{"label":"Sold to number","field":"sold_to_number","filter":true},{"label":"Vendor name","field":"vendor_name","filter":true},{"label":"Reseller name","field":"reseller_name","filter":true},{"label":"Vendor id","field":"vendor_id","filter":true},{"label":"Company name","field":"company_name","filter":true},{"label":"Address","field":"address","filter":true},{"label":"End customer address 2","field":"end_customer_address_2","filter":true},{"label":"End customer address 3","field":"end_customer_address_3","filter":true},{"label":"City","field":"city","filter":true},{"label":"State","field":"state","filter":true},{"label":"Postal code","field":"postal_code","filter":true},{"label":"Country","field":"country","filter":true},{"label":"End customer account type","field":"end_customer_account_type","filter":true},{"label":"Contact name","field":"contact_name","filter":true},{"label":"Email","field":"email","filter":true},{"label":"End customer contact phone","field":"end_customer_contact_phone","filter":true},{"label":"End customer industry segment","field":"end_customer_industry_segment","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Subscription reference","field":"subscription_reference","filter":true},{"label":"Sketchup agreement start date","field":"sketchup_agreement_start_date","filter":true},{"label":"Sketchup agreement end date","field":"sketchup_agreement_end_date","filter":true},{"label":"Agreement terms","field":"agreement_terms","filter":true},{"label":"Agreement type","field":"agreement_type","filter":true},{"label":"Agreement status","field":"agreement_status","filter":true},{"label":"Agreement support level","field":"agreement_support_level","filter":true},{"label":"Sketchup agreement days due","field":"sketchup_agreement_days_due","filter":true},{"label":"Sketchup agreement autorenew","field":"sketchup_agreement_autorenew","filter":true},{"label":"Sketchup product name","field":"sketchup_product_name","filter":true},{"label":"Product family","field":"product_family","filter":true},{"label":"Product market segment","field":"product_market_segment","filter":true},{"label":"Sketchup product release","field":"sketchup_product_release","filter":true},{"label":"Product type","field":"product_type","filter":true},{"label":"Product deployment","field":"product_deployment","filter":true},{"label":"Product sku","field":"product_sku","filter":true},{"label":"Product sku description","field":"product_sku_description","filter":true},{"label":"Product part","field":"product_part","filter":true},{"label":"Product list price","field":"product_list_price","filter":true},{"label":"Sketchup product list price currency","field":"sketchup_product_list_price_currency","filter":true},{"label":"Sketchup subscription id","field":"sketchup_subscription_id","filter":true},{"label":"Subscription serial number","field":"subscription_serial_number","filter":true},{"label":"Status","field":"status","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"Sketchup subscription start date","field":"sketchup_subscription_start_date","filter":true},{"label":"Sketchup subscription end date","field":"sketchup_subscription_end_date","filter":true},{"label":"End customer contact name","field":"end_customer_contact_name","filter":true},{"label":"Sketchup subscription contact email","field":"sketchup_subscription_contact_email","filter":true},{"label":"Subscription level","field":"subscription_level","filter":true},{"label":"Sketchup subscription days due","field":"sketchup_subscription_days_due","filter":true},{"label":"Quotation id","field":"quotation_id","filter":true},{"label":"Quotation type","field":"quotation_type","filter":true},{"label":"Sketchup quotation vendor id","field":"sketchup_quotation_vendor_id","filter":true},{"label":"Sketchup quotation deal registration number","field":"sketchup_quotation_deal_registration_number","filter":true},{"label":"Quotation status","field":"quotation_status","filter":true},{"label":"Sketchup quotation resellerpo previous","field":"sketchup_quotation_resellerpo_previous","filter":true},{"label":"Sketchup quotation due date","field":"sketchup_quotation_due_date","filter":true},{"label":"Flaer phase","field":"flaer_phase","filter":true},{"label":"Sketchup updated","field":"sketchup_updated","filter":true}]
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:195] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-09-05 14:04:13] [sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php:350] Final safety check - columns count: 58, fallback count: 62
