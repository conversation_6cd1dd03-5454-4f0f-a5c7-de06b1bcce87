<?php
namespace system;
use system\database;
/**
 * Data Table Storage Class
 * Handles database operations for data table configurations
 */
class data_table_storage {

    /**
     * Get data table configuration from database
     *
     * @param string|null $table_name The table identifier (optional if data_source_id provided)
     * @param int|null $user_id User ID (null for global config)
     * @param int|null $data_source_id Data source identifier (optional if table_name provided)
     * @return array|null Configuration array or null if not found
     */
    public static function get_configuration(?string $table_name = null, ?int $user_id = null, ?int $data_source_id = null): array|string|null {
        try {
            print_rr($table_name,'$table_name',full:true);
            //
            if (!$table_name && !$data_source_id) {
                return print_rr('Must have either table_name or data_source_id','data_table_storage_get_error',return:true);
            }

            $query = database::table('autobooks_data_table_storage')->select(['configuration', 'data_source_id', 'updated_at', 'table_name','user_id']);
            if ($user_id) {
                $query->where('user_id', $user_id);
            } else {
                $query->where('user_id', null);
            }
            $query->where('table_name', $table_name);

            $result = $query->first();

            if ($result) {
                return [
                    'configuration' => json_decode($result['configuration'], true) ?? [],
                    'data_source_id' => $result['data_source_id'],
                    'table_name' => $result['table_name'],
                    'updated_at' => $result['updated_at']
                ];
            }
            return print_rr(['result'=>$result,'query'=> $query->toSql()],label:'data_table_storage_get_error',return:true);
        } catch (\Exception $e) {
            // Handle error gracefully
            return print_rr($e->getMessage(),'data_table_storage_get_error',return:true);
        }
    }

    /**
     * Save data table configuration to database
     *
     * @param string|null $table_name The table identifier (optional if data_source_id provided)
     * @param array $configuration Configuration array
     * @param int|null $user_id User ID (null for global config)
     * @param int|null $data_source_id Data source identifier (optional if table_name provided)
     * @return bool Success status
     */
    public static function save_configuration(?string $table_name = null, array $configuration = [], ?int $user_id = null, ?int $data_source_id = null): bool {
        try {
            // Must have either table_name or data_source_id
            if (!$table_name && !$data_source_id) {
                return false;
            }
            // Check if configuration exists for this table_name and user_id combination
            $existing = null;

            if ($table_name) {
                $query = database::table('autobooks_data_table_storage')
                    ->where('table_name', $table_name);

                if ($user_id) {
                    $query->where('user_id', $user_id);
                } else {
                    $query->whereNull('user_id');
                }

                $existing = $query->first();

                tcs_log("Checking for existing configuration: table_name='{$table_name}', user_id=" . ($user_id ?? 'null') . ", found=" . ($existing ? 'yes' : 'no'), 'data_table_saga');
            }

            $data = [
                'table_name' => $table_name,
                'user_id' => $user_id,
                'configuration' => json_encode($configuration),
                'data_source_id' => $data_source_id
            ];

            // Debug output
            tcs_log(['configuration' => $configuration, 'table_name' => $table_name, 'data_source_id'=>$data_source_id], 'data_table_saga');

            if ($existing) {
                // Update existing record
                $update_query = database::table('autobooks_data_table_storage')
                    ->where('id', $existing['id']); // Use ID for precise updates

                $update_data = [
                    'configuration' => json_encode($configuration),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // Update table_name and data_source_id if provided
                if ($table_name) {
                    $update_data['table_name'] = $table_name;
                }
                if ($data_source_id) {
                    $update_data['data_source_id'] = $data_source_id;
                }

                $update_query->update($update_data);
            } else {
                // Insert new record
                database::table('autobooks_data_table_storage')->insert($data);
            }

            return true;
        } catch (Exception $e) {
            // Log error for debugging if needed
            // print_rr($e->getMessage(),'data_table_storage_save_error');
            return false;
        }
    }

    /**
     * Delete data table configuration
     *
     * @param string|null $table_name The table identifier (optional if data_source_id provided)
     * @param int|null $user_id User ID (null for global config)
     * @param int|null $data_source_id Data source identifier (optional if table_name provided)
     * @return bool Success status
     */
    public static function delete_configuration(?string $table_name = null, ?int $user_id = null, ?int $data_source_id = null): bool {
        try {
            // Must have either table_name or data_source_id
            if (!$table_name && !$data_source_id) {
                return false;
            }

            $query = database::table('autobooks_data_table_storage');

            if ($table_name) {
                $query->where('table_name', $table_name);
            }

            if ($data_source_id) {
                $query->where('data_source_id', $data_source_id);
            }

            if ($user_id) {
                $query->where('user_id', $user_id);
            } else {
                $query->where('user_id', null);
            }

            $query->delete();
            return true;
        } catch (Exception $e) {
            // Handle error gracefully
            return false;
        }
    }

    /**
     * Initialize default configuration from provided data
     *
     * @param string|null $table_name The table identifier (optional if data_source_id provided)
     * @param array $columns Original columns array
     * @param int|null $user_id User ID (null for global config)
     * @param int|null $data_source_id Data source identifier (optional if table_name provided)
     * @return array The initialized configuration
     */
    public static function initialize_default_configuration(?string $table_name = null, array $columns = [], ?int $user_id = null, ?int $data_source_id = null): array {
        $structure = [];
        $hidden = [];

        foreach ($columns as $index => $col) {
            $column_id = 'col_' . $index . '_' . md5($col['label']);

            // Use fields array if available, otherwise fall back to field for legacy columns
            $fields_array = $col['fields'] ?? (is_array($col['field']) ? $col['field'] : [$col['field']]);

            $structure[] = [
                'id' => $column_id,
                'label' => $col['label'],
                'fields' => $fields_array,
                'filter' => $col['filter'] ?? false,
                'visible' => true,
                'action_buttons' => $col['action_buttons'] ?? []
            ];
        }

        // Check if there's an existing configuration to preserve data source settings
        // Look for any existing configuration for this table, regardless of data_source_id
        $existing_config = null;
        try {
            $query = database::table('autobooks_data_table_storage')
                ->select(['configuration', 'data_source_id', 'updated_at', 'table_name','user_id'])
                ->where('table_name', $table_name);

            if ($user_id) {
                $query->where('user_id', $user_id);
            } else {
                $query->where('user_id', null);
            }

            $result = $query->first();
            if ($result) {
                $existing_config = [
                    'configuration' => json_decode($result['configuration'], true) ?? [],
                    'data_source_id' => $result['data_source_id'],
                    'table_name' => $result['table_name'],
                    'updated_at' => $result['updated_at']
                ];
            }
        } catch (Exception $e) {
            // Handle error gracefully
        }

        $existing_data_source_type = 'hardcoded';
        $existing_data_source_id = null;

        if ($existing_config) {
            $existing_data_source_type = $existing_config['configuration']['data_source_type'] ?? 'hardcoded';
            $existing_data_source_id = $existing_config['data_source_id'] ?? $existing_config['configuration']['data_source_id'] ?? null;

            // Debug logging
            tcs_log("Found existing config for table '$table_name': type=$existing_data_source_type, id=$existing_data_source_id", 'data_table_saga');
        } else {
            // Debug logging
            tcs_log("No existing config found for table '$table_name', using defaults", 'data_table_saga');
        }

        $configuration = [
            'hidden' => $hidden,
            'structure' => $structure,
            'columns' => $columns, // Store original columns for reference
            'data_source_type' => $existing_data_source_type, // Preserve existing data source type
            'data_source_id' => $existing_data_source_id, // Preserve existing data source ID
            'created_at' => date('Y-m-d H:i:s')
        ];

        // Use the existing data source ID if available, otherwise use the passed parameter
        $save_data_source_id = $existing_data_source_id ?? $data_source_id;

        // Save to database
        self::save_configuration($table_name, $configuration, $user_id, $save_data_source_id);

        return $configuration;
    }

    /**
     * Get or create configuration
     * Returns existing configuration or creates default from provided data
     *
     * @param string|null $table_name The table identifier (optional if data_source_id provided)
     * @param array $columns Original columns array (used for default creation)
     * @param int|null $user_id User ID (null for global config)
     * @param int|null $data_source_id Data source identifier (optional if table_name provided)
     * @return array Configuration array
     */
    public static function get_or_create_configuration(?string $table_name = null, array $columns = [], ?int $user_id = null, ?int $data_source_id = null): array {
        $stored = self::get_configuration($table_name, $user_id, $data_source_id);

        if ($stored) {
            return $stored['configuration'];
        }

        // If no columns provided, return blank configuration
        if (empty($columns)) {
            return [
                'hidden' => [],
                'structure' => [],
                'columns' => [],
                'data_source_type' => 'hardcoded',
                'created_at' => date('Y-m-d H:i:s')
            ];
        }

        // Initialize default configuration
        return self::initialize_default_configuration($table_name, $columns, $user_id, $data_source_id);
    }

    /**
     * Get data for a table configuration, either from hardcoded data or data source
     *
     * @param string|null $table_name The table identifier (optional if data_source_id provided)
     * @param array $hardcoded_data Fallback hardcoded data
     * @param array $criteria Additional criteria for data source queries
     * @param int|null $user_id User ID (null for global config)
     * @param int|null $data_source_id Data source identifier (optional if table_name provided)
     * @return array Data result with success status
     */
    public static function get_table_data(?string $table_name = null, array $hardcoded_data = [], array $criteria = [], ?int $user_id = null, ?int $data_source_id = null): array {
        try {
            $config = self::get_configuration($table_name, $user_id, $data_source_id);
            print_rr($config, 'tcs_get_table_data');
            if (!$config || !is_array($config)) {
                // No configuration exists, return hardcoded data
                return [
                    'success' => true,
                    'data' => $hardcoded_data,
                    'source' => 'hardcoded',
                    'count' => count($hardcoded_data),
                    'total_count' => count($hardcoded_data)
                ];
            }

            $configuration = $config['configuration'];
            $data_source_type = $configuration['data_source_type'] ?? 'hardcoded';
            $data_source_id = $config['data_source_id'] ?? $configuration['data_source_id'] ?? null;

            // Debug output
            tcs_log("Getting table data: table=$table_name, type=$data_source_type, id=$data_source_id", 'data_table_saga');

            if ($data_source_type === 'data_source' && $data_source_id) {
                // Use data source
                $data_source_result = data_source_manager::get_data_source_data($data_source_id, $criteria);
                tcs_log('Using data source: ' . $data_source_id, 'data_table_saga');
                print_rr($data_source_result, 'tcs_get_table_data_data_source_result');
                if ($data_source_result['success']) {
//                    tcs_log('data_source_result: ' . print_r([
//                            'success' => true,
//                            'data' => $data_source_result['data'],
//                            'source' => 'data_source',
//                            'data_source_id' => $data_source_id,
//                            'count' => $data_source_result['count'],
//                            'total_count' => $data_source_result['total_count'] ?? $data_source_result['count'],
//                            'criteria' => $criteria,
//                            'query' => $data_source_result['query'] ?? null], true), 'data_table_saga');
                    return [
                        'success' => true,
                        'data' => $data_source_result['data'],
                        'config' => $configuration,
                        'source' => 'data_source',
                        'columns' => $data_source_result['columns'] ?? [],
                        'available_fields' => self::extract_available_fields_from_config($configuration),
                        'data_source_id' => $data_source_id,
                        'count' => $data_source_result['count'],
                        'total_count' => $data_source_result['total_count'] ?? $data_source_result['count'],
                        'criteria' => $criteria,
                        'query' => $data_source_result['query'] ?? null
                    ];
                } else {
                    // Data source failed, fall back to hardcoded
                    tcs_log('Data source failed, falling back to hardcoded data: ' . ($data_source_result['error'] ?? 'Unknown error'), 'data_table_saga');
                    return [
                        'success' => true,
                        'data' => $hardcoded_data,
                        'source' => 'hardcoded_fallback',
                        'error' => $data_source_result['error'] ?? 'Data source failed',
                        'count' => count($hardcoded_data),
                        'total_count' => count($hardcoded_data)
                    ];
                }
            } else {
                // Use hardcoded data
                tcs_log('Using hardcoded data', 'data_table_saga');
                return [
                    'success' => true,
                    'data' => $hardcoded_data,
                    'config' => $configuration,
                    'source' => 'hardcoded',
                    'available_fields' => self::extract_available_fields_from_config($configuration),
                    'count' => count($hardcoded_data),
                    'total_count' => count($hardcoded_data)
                ];
            }

        } catch (Exception $e) {
            // Error occurred, fall back to hardcoded data
            return [
                'success' => true,
                'data' => $hardcoded_data,
                'source' => 'hardcoded_fallback',
                'error' => $e->getMessage(),
                'count' => count($hardcoded_data)
            ];
        }
    }

    /**
     * Get current user ID from session/auth
     *
     * @return int|null User ID or null if not authenticated
     */
    public static function get_current_user_id(): ?int {
        // Check if users class exists and user is authenticated
        if (class_exists('users')) {
            $user = users::checkAuth();
            return $user['id'] ?? null;
        }

        // Fallback to session if available
        return $_SESSION['user_id'] ?? null;
    }

    /**
     * List all configurations for a user or globally
     *
     * @param int|null $user_id User ID (null for global configs)
     * @return array Array of configurations
     */
    public static function list_configurations(?int $user_id = null): array {
        try {
            $query = database::table('autobooks_data_table_storage')
                ->select(['table_name', 'data_source_id', 'updated_at']);

            if ($user_id) {
                $query->where('user_id', $user_id);
            } else {
                $query->where('user_id', null);
            }

            $results = $query->orderBy('updated_at', 'DESC')->get();

            $configurations = [];
            foreach ($results as $row) {
                $configurations[] = [
                    'table_name' => $row['table_name'],
                    'data_source_id' => $row['data_source_id'],
                    'updated_at' => $row['updated_at']
                ];
            }

            return $configurations;
        } catch (Exception $e) {
            // Handle error gracefully
            return [];
        }
    }

    /**
     * Get configuration by data source ID only
     *
     * @param int $data_source_id Data source identifier
     * @param int|null $user_id User ID (null for global config)
     * @return array|null Configuration array or null if not found
     */
    public static function get_configuration_by_data_source_id(int $data_source_id, ?int $user_id = null): ?array {
        return self::get_configuration(null, $user_id, $data_source_id);
    }

    /**
     * Save configuration by data source ID only
     *
     * @param int $data_source_id Data source identifier
     * @param array $configuration Configuration array
     * @param int|null $user_id User ID (null for global config)
     * @param string|null $table_name Optional table name to associate
     * @return bool Success status
     */
    public static function save_configuration_by_data_source_id(int $data_source_id, array $configuration, ?int $user_id = null, ?string $table_name = null): bool {
        return self::save_configuration($table_name, $configuration, $user_id, $data_source_id);
    }

    /**
     * Delete configuration by data source ID only
     *
     * @param int $data_source_id Data source identifier
     * @param int|null $user_id User ID (null for global config)
     * @return bool Success status
     */
    public static function delete_configuration_by_data_source_id(int $data_source_id, ?int $user_id = null): bool {
        return self::delete_configuration(null, $user_id, $data_source_id);
    }

    /**
     * List configurations by data source ID
     *
     * @param int $data_source_id Data source identifier
     * @param int|null $user_id User ID (null for global configs)
     * @return array Array of configurations
     */
    public static function list_configurations_by_data_source_id(int $data_source_id, ?int $user_id = null): array {
        try {
            $query = database::table('autobooks_data_table_storage')
                ->select(['table_name', 'data_source_id', 'updated_at', 'configuration'])
                ->where('data_source_id', $data_source_id);

            if ($user_id) {
                $query->where('user_id', $user_id);
            } else {
                $query->where('user_id', null);
            }

            $results = $query->orderBy('updated_at', 'DESC')->get();

            $configurations = [];
            foreach ($results as $row) {
                $configurations[] = [
                    'table_name' => $row['table_name'],
                    'data_source_id' => $row['data_source_id'],
                    'updated_at' => $row['updated_at'],
                    'configuration' => json_decode($row['configuration'], true) ?? []
                ];
            }

            return $configurations;
        } catch (Exception $e) {
            // Handle error gracefully
            return [];
        }
    }

    /**
     * Clean up configurations when a data source is deleted
     *
     * @param int $data_source_id Data source identifier to clean up
     * @return bool Success status
     */
    public static function cleanup_data_source_configurations(int $data_source_id): bool {
        try {
            // Delete all configurations associated with this data source
            database::table('autobooks_data_table_storage')
                ->where('data_source_id', $data_source_id)
                ->delete();

            return true;
        } catch (Exception $e) {
            // Handle error gracefully
            return false;
        }
    }

    /**
     * Update data source ID for existing configurations
     *
     * @param int $old_data_source_id Old data source identifier
     * @param int $new_data_source_id New data source identifier
     * @return bool Success status
     */
    public static function update_data_source_id(int $old_data_source_id, int $new_data_source_id): bool {
        try {
            database::table('autobooks_data_table_storage')
                ->where('data_source_id', $old_data_source_id)
                ->update([
                    'data_source_id' => $new_data_source_id,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            return true;
        } catch (Exception $e) {
            // Handle error gracefully
            return false;
        }
    }

    /**
     * Check if a data source has any associated configurations
     *
     * @param int $data_source_id Data source identifier
     * @return bool True if configurations exist
     */
    public static function has_data_source_configurations(int $data_source_id): bool {
        try {
            $count = database::table('autobooks_data_table_storage')
                ->where('data_source_id', $data_source_id)
                ->count();

            return $count > 0;
        } catch (Exception $e) {
            // Handle error gracefully
            return false;
        }
    }

    /**
     * Extract available fields from configuration for column manager
     *
     * @param array $configuration Table configuration
     * @return array List of available field names
     */
    private static function extract_available_fields_from_config(array $configuration): array {
        $available_fields = [];

        // Check if we have available_fields from unified field matching
        if (!empty($configuration['available_fields'])) {
            foreach ($configuration['available_fields'] as $field_info) {
                if (is_array($field_info) && isset($field_info['field'])) {
                    $available_fields[] = $field_info['field'];
                } elseif (is_string($field_info)) {
                    $available_fields[] = $field_info;
                }
            }
        }

        // Also include any fields from all_columns that aren't currently visible
        if (!empty($configuration['all_columns'])) {
            foreach ($configuration['all_columns'] as $column) {
                if (isset($column['field']) && !$column['visible']) {
                    $field_name = $column['field'];
                    if (!in_array($field_name, $available_fields)) {
                        $available_fields[] = $field_name;
                    }
                }
            }
        }

        return $available_fields;
    }

    /**
     * Prepare all template data by processing columns, data sources, and configuration
     *
     * @param array $params Template parameters
     * @return array Processed template data
     */
    public static function prepare_template_data(array $params): array {
        // Set defaults
    //    print_rr($params, null, 'tcs_params');
        $columns = $params['columns'] ?? [];
        $items = $params['items'] ?? [];
        $available_fields = $params['available_fields'] ?? [];
        $table_name = $params['table_name'] ?? str_replace('/','_',strtolower(APP_PATH . '_' . CURRENT_PAGE));
        $criteria = $params['criteria'] ?? [];
        $data_source_id = $params['data_source_id'] ?? null;
        $data_source_type = $params['data_source_type'] ?? 'hardcoded';
        $callback = $params['callback'] ?? null;
        $column_preferences = $params['column_preferences'] ?? [];
        $config = $params['config'] ?? null;
        $user_id = $_SESSION['user_id'] ?? null;

        print_rr($column_preferences,  'tcs_column_preferences');
        // Auto-load data from data source if no items provided and no callback
        if (empty($items) && empty($callback)) {
            $data_result = self::get_table_data($table_name, [], $criteria, $user_id, $data_source_id);
            $columns = count($columns) > 0 ? $columns : ($data_result['config']['columns'] ?? []);

            // Build available fields from columns
            if (is_array($columns)) {
                foreach ($columns as $col) {
                    $available_fields[] = $col['field'];
                }
            }

            tcs_log("Auto-loading data: table=$table_name, success=" . ($data_result['success'] ? 'true' : 'false') . ", source=" . ($data_result['source'] ?? 'unknown') . ", count=" . ($data_result['count'] ?? 0), 'data_table_saga');

            if ($data_result['success']) {
                $items = $data_result['data'];

                // Auto-generate columns from data source if no columns provided
                if (!empty($items) && empty($columns)) {
                    $columns = [];
                    $available_fields = [];
                    $first_item = reset($items);
                    if ($first_item) {
                        foreach (array_keys($first_item) as $field) {
                            $columns[] = [
                                'label' => ucwords(str_replace('_', ' ', $field)),
                                'field' => $field,
                                'filter' => false,
                                'extra_parameters' => ''
                            ];
                            $available_fields[] = $field;
                        }
                    }
                }
            }

            $config = $data_result['config'] ?? null;
            $column_preferences = $config ?? [];
        }

        print_rr($column_preferences, null, 'tcs_column_preferences');
        // Get configuration if not already set
        if (!$config) {
            $config = self::get_configuration($table_name, $user_id);
        }

        print_rr($column_preferences, 'tcs_column_preferences');
        // Set column preferences from config if not provided
        if (empty($column_preferences)) {
            if ($config && isset($config['configuration'])) {
                $column_preferences = $config['configuration'] ?? [];
            }
        }
        print_rr($column_preferences,  'tcs_column_preferences');
        // Process columns from structure if available
        $columns = self::process_columns_from_structure($columns, $column_preferences);

        // Determine data source information
        if ($config) {
            if (!empty($config['data_source_id'])) {
                $data_source_type = 'data_source';
                $data_source_id = $config['data_source_id'];
            } elseif (!empty($column_preferences)) {
                $data_source_type = $column_preferences['data_source_type'] ?? 'hardcoded';
                $data_source_id = $column_preferences['data_source_id'] ?? null;
            }
        }

        // Handle data source override
        if ($data_source_type === 'data_source' && $data_source_id) {
            $override_result = self::handle_data_source_override($table_name, $items, $criteria, $user_id, $data_source_id);
            if ($override_result['success']) {
                $items = $override_result['data'];
                $available_fields = $override_result['available_fields'] ?? $available_fields;
            }
        }

        return [
            'items' => $items,
            'columns' => $columns,
            'available_fields' => $available_fields,
            'table_name' => $table_name,
            'data_source_type' => $data_source_type,
            'data_source_id' => $data_source_id,
            'column_preferences' => $column_preferences,
            'config' => $config
        ];
    }

    /**
     * Process columns from structure configuration
     *
     * @param array $columns Original columns array
     * @param array $column_preferences Column preferences configuration
     * @return array Processed columns array
     */
    public static function process_columns_from_structure(array $columns, array $column_preferences = []): array {
        if (empty($column_preferences['structure'])) {
            return $columns;
        }

        $structure_columns = [];
        foreach ($column_preferences['structure'] as $struct_col) {
            // Find the original column definition to preserve other properties
            $original_col = null;
            if (is_array($columns)) {
                foreach ($columns as $col) {
                    if ($col['label'] === $struct_col['label'] || (isset($col['field']) && in_array($col['field'], $struct_col['fields'] ?? []))) {
                        $original_col = $col;
                        break;
                    }
                }
            }

            // Build the column using structure data with original properties as fallback
            $fields_array = $struct_col['fields'] ?? [$original_col['field'] ?? ''];
            $structure_columns[] = [
                'label' => $struct_col['label'],
                'fields' => $fields_array,
                'filter' => $struct_col['filter'] ?? ($original_col['filter'] ?? true),
                'visible' => $struct_col['visible'] ?? true,
                'structure_id' => $struct_col['id'],
                'action_buttons' => $struct_col['action_buttons'] ?? [],
                // Preserve other original properties
                'replacements' => $original_col['replacements'] ?? null,
                'content' => $original_col['content'] ?? null,
                'class' => $original_col['class'] ?? '',
                'extra_parameters' => $original_col['extra_parameters'] ?? ''
            ];
        }

        return $structure_columns;
    }

    /**
     * Handle data source override for hardcoded data
     *
     * @param string $table_name Table identifier
     * @param array $items Current items array
     * @param array $criteria Search criteria
     * @param int|null $user_id User ID
     * @param int|null $data_source_id Data source ID
     * @return array Override result
     */
    public static function handle_data_source_override(string $table_name, array $items, array $criteria, ?int $user_id, ?int $data_source_id): array {
        $data_result = self::get_table_data($table_name, $items, $criteria, $user_id, $data_source_id);

        tcs_log("Overriding hardcoded data with data source: table=$table_name, success=" . ($data_result['success'] ? 'true' : 'false') . ", source=" . ($data_result['source'] ?? 'unknown') . ", count=" . ($data_result['count'] ?? 0), 'data_table_saga');

        if ($data_result['success'] && $data_result['source'] !== 'hardcoded_fallback') {
            $available_fields = [];
            if (!empty($data_result['columns'])) {
                foreach ($data_result['columns'] as $col) {
                    $available_fields[] = $col['field'] ?? $col;
                }
            }

            return [
                'success' => true,
                'data' => $data_result['data'],
                'available_fields' => $available_fields
            ];
        }

        return [
            'success' => false,
            'data' => $items,
            'available_fields' => []
        ];
    }

    /**
     * Process column visibility and field information for display
     *
     * @param array $col Column definition
     * @param array $column_preferences Column preferences
     * @param int $loop_index Current loop index
     * @return array Processed column information
     */
    public static function process_column_for_display(array $col, array $column_preferences, int $loop_index): array {
        // Use structure data if available
        if (isset($col['structure_id'])) {
            $column_id = $col['structure_id'];
            $is_hidden = !$col['visible'];
            // Ensure $col_fields is a flat array of field names
            $col_fields = is_array($col['fields']) ? $col['fields'] : [$col['fields']];
            // Flatten nested arrays if they exist
            $col_fields = array_merge(...array_map(fn($f) => is_array($f) ? $f : [$f], $col_fields));
            $col_label = $col['label'];
        } else {
            // Fallback for non-structure columns - use fields if available, otherwise field
            $column_id = 'col_' . $loop_index . '_' . md5($col['label']);
            $is_hidden = isset($column_preferences['hidden']) && in_array($column_id, $column_preferences['hidden']);
            $col_fields = $col['fields'] ?? (is_array($col['field']) ? $col['field'] : [$col['field']]);
            $col_label = $col['label'];
        }

        return [
            'column_id' => $column_id,
            'is_hidden' => $is_hidden,
            'col_fields' => $col_fields,
            'col_label' => $col_label
        ];
    }

    /**
     * Process field values for display, handling multiple fields and nested arrays
     *
     * @param array $col_fields Array of field names
     * @param array $item Data item
     * @return string Processed field values
     */
    public static function process_field_values(array $col_fields, array $item): string {
        $field_values = [];
        foreach ($col_fields as $field) {
            if (is_array($field)) {
                // Handle nested array
                foreach ($field as $nested_field) {
                    if (isset($item[$nested_field])) {
                        $field_values[] = $item[$nested_field];
                    }
                }
            } else {
                // Handle regular field
                if (isset($item[$field])) {
                    $field_values[] = $item[$field];
                }
            }
        }
        return implode('<br>', $field_values);
    }
}
